package main

import (
	"gitlab.papegames.com/fringe/sparrow"

	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/shorturl/database"
	"gitlab.papegames.com/fringe/shorturl/server"
	"gitlab.papegames.com/fringe/shorturl/service"
)

func main() {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		server.Startup,
		service.Startup,
	).Server(server.Get()...).Launch()
}
