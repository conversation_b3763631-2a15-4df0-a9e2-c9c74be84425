package service

import (
	"context"
	"os"
	"path"
	"runtime"
	"strings"
	"testing"

	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/shorturl/database"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/compose"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
)

func TestMain(m *testing.M) {
	_, filename, _, _ := runtime.Caller(0)
	c := path.Join(path.Dir(filename), "docker-compose.yaml")
	compose.Setup(c)
	err := xconf.ReadInConfig()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}

	err = config.Startup()
	if err != nil {
		panic(err)
	}
	err = Startup()
	if err != nil {
		panic(err)
	}
	exitCode := m.Run()
	compose.Down()
	os.Exit(exitCode)
}

func MustLink(shint string) *Link {
	l, _ := GetLink(shint)
	return l
}

func TestNewLink(t *testing.T) {
	Convey("test newlink", t, func() {
		ctx := context.Background()

		Convey("when create link with valid uri and scene", func() {
			id, path, err := GetDefaultLink().New(ctx, "https://example.com", "default")
			So(err, ShouldBeNil)
			So(id, ShouldNotBeEmpty)
			So(path, ShouldNotBeEmpty)
		})

		Convey("test short charset create", func() {
			id, path, err := MustLink("gameshare").New(ctx, "https://example.com", "gameshare")
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 6)
			So(strings.ToUpper(id), ShouldEqual, id)
			So(path, ShouldNotBeEmpty)
		})

		Convey("when create link with secret", func() {
			id1, path2, err := GetDefaultLink().New(ctx, "https://example.com", "secret")
			So(err, ShouldBeNil)
			So(path2, ShouldNotBeEmpty)
			So(len(id1), ShouldEqual, 17)
			So(id1[6], ShouldEqual, ':')

			id2, path2, err := GetDefaultLink().New(ctx, "https://example.com", "secret")
			So(err, ShouldBeNil)
			So(path2, ShouldNotBeEmpty)
			So(len(id2), ShouldEqual, 17)
			So(id2[6], ShouldEqual, ':')

			So(id1, ShouldNotEqual, id2)
			So(id1[:5], ShouldEqual, id2[:5])
			So(id1[7:], ShouldNotEqual, id2[7:])
		})

		Convey("when create link with empty uri", func() {
			_, _, err := GetDefaultLink().New(ctx, "", "default")
			So(err, ShouldBeNil)
		})

		Convey("when create link with json data", func() {
			jsonContent := `{"clientId":"1087","nid":"","token":"","roleId":"","gameLang":"ko","zoneId":"","platId":"90","deviceId":"0B4C3CE30DD44E4ABBCDB8D7DD0DBEDBT1733224683"}`
			id, _, err := GetDefaultLink().New(ctx, jsonContent, "default")
			So(err, ShouldBeNil)
			d, err := GetDefaultLink().Query(ctx, id)
			So(err, ShouldBeNil)
			So(d, ShouldEqual, jsonContent)
		})

		Convey("when create link with very long uri", func() {
			longURI := "https://example.com/" + strings.Repeat("a", 2048)
			_, _, err := GetDefaultLink().New(ctx, longURI, "default")
			So(err, ShouldBeNil)
		})

		Convey("when create link with invalid scene", func() {
			_, _, err := GetDefaultLink().New(ctx, "https://example.com", "invalid_scene")
			So(err, ShouldNotBeNil)
		})

		Convey("when create link with empty scene", func() {
			_, _, err := GetDefaultLink().New(ctx, "https://example.com", "")
			So(err, ShouldBeNil)
		})

		Convey("when create link with special characters in uri", func() {
			_, _, err := GetDefaultLink().New(ctx, "https://example.com/?param=value&param2=value2", "default")
			So(err, ShouldBeNil)
		})

		Convey("when create link with unicode characters in uri", func() {
			_, _, err := GetDefaultLink().New(ctx, "https://example.com/测试", "default")
			So(err, ShouldBeNil)
		})
	})
}

func TestNewID(t *testing.T) {
	Convey("test newid", t, func() {
		ctx := context.Background()

		Convey("when generate new id", func() {
			id, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(id, ShouldNotBeEmpty)
			So(len(id), ShouldEqual, 6) // default length for first range
		})

		Convey("when generate multiple ids", func() {
			id1, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			id2, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(id1, ShouldNotEqual, id2)
		})
	})
}

func TestNewIDRanges(t *testing.T) {
	Convey("test id ranges", t, func() {
		ctx := context.Background()
		// {6, 56800235584},
		// {7, 3521614606208},
		// {8, 218340105584896},
		// {9, 13537086546263552},
		// {10, 839299365868340224},
		Convey("when generate id at range boundaries", func() {
			// Test first range (length 6)
			id, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 6)

			// Test first range (length 6) max value is 56800235583
			key := config.Get().Shint[ShintDefault].Key + ":0"
			err = database.Get().Set(ctx, key, 56800235584-2, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 6)

			// Test second range (length 7) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 56800235584-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 7)

			// Test third range (length 8) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 3521614606208-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 8)

			// Test fourth range (length 9) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 218340105584896-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 9)

			// Test fifth range (length 10) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 13537086546263552-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 10)

			err = database.Get().Set(ctx, key, 839299365868340224-2, 0).Err()
			So(err, ShouldBeNil)
			id, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 10)

			// Test out of range by setting counter to max value
			err = database.Get().Set(ctx, key, 839299365868340224-1, 0).Err()
			So(err, ShouldBeNil)
			_, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldNotBeNil)
		})

		Convey("when short charset generate id at range boundaries", func() {
			// Test first range (length 6)
			id, err := MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 6)

			// Test first range (length 6) max value is 56800235583
			key := config.Get().Shint["gameshare"].Key + ":0"
			err = database.Get().Set(ctx, key, 56800235584-2, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 6)

			// Test second range (length 7) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 56800235584-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 7)

			// Test third range (length 8) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 3521614606208-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 8)

			// Test fourth range (length 9) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 218340105584896-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 9)

			// Test fifth range (length 10) by setting counter to boundary value
			err = database.Get().Set(ctx, key, 13537086546263552-1, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 10)

			err = database.Get().Set(ctx, key, 839299365868340224-2, 0).Err()
			So(err, ShouldBeNil)
			id, err = MustLink("gameshare").NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id), ShouldEqual, 10)

			// Test out of range by setting counter to max value
			err = database.Get().Set(ctx, key, 839299365868340224-1, 0).Err()
			So(err, ShouldBeNil)
			_, err = GetDefaultLink().NewID(ctx)
			So(err, ShouldNotBeNil)
		})

		Convey("when generate ids in sequence", func() {
			// Reset counter by removing the key from Redis
			key := config.Get().Shint[ShintDefault].Key + ":0"
			err := database.Get().Del(ctx, key).Err()
			So(err, ShouldBeNil)

			// Test sequence of IDs
			id1, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id1), ShouldEqual, 6)

			id2, err := GetDefaultLink().NewID(ctx)
			So(err, ShouldBeNil)
			So(len(id2), ShouldEqual, 6)
			So(id1, ShouldNotEqual, id2)
		})
	})
}

func TestQuery(t *testing.T) {
	Convey("test query", t, func() {
		ctx := context.Background()
		Convey("when query existing link", func() {
			// First create a new link
			id, _, err := GetDefaultLink().New(ctx, "https://example.com", "default")
			So(err, ShouldBeNil)

			// Then query it directly from Redis
			key := Prefix + id
			uri, err := database.Get().Get(ctx, key).Result()
			So(err, ShouldBeNil)
			So(uri, ShouldEqual, "https://example.com")

			// Then query it using the service
			uri, err = GetDefaultLink().Query(ctx, id)
			So(err, ShouldBeNil)
			So(uri, ShouldEqual, "https://example.com")

			id2, _, err := GetDefaultLink().New(ctx, "https://example.com", "secret")
			So(err, ShouldBeNil)
			So(id2, ShouldNotEqual, id)
			So(len(id2), ShouldEqual, 17)

			// Then query it directly from Redis
			key = Prefix + id2
			uri, err = database.Get().Get(ctx, key).Result()
			So(err, ShouldBeNil)
			So(uri, ShouldEqual, "https://example.com")

			// Then query it using the service
			uri, err = GetDefaultLink().Query(ctx, id)
			So(err, ShouldBeNil)
			So(uri, ShouldEqual, "https://example.com")

		})

		Convey("when query non-existing link", func() {
			key := "link:nonexistent"
			_, err := database.Get().Get(ctx, key).Result()
			So(err, ShouldNotBeNil)

			// Then query it using the service
			_, err = GetDefaultLink().Query(ctx, "nonexistent")
			So(err, ShouldNotBeNil)
		})

		Convey("when query link with empty id", func() {
			key := Prefix
			_, err := database.Get().Get(ctx, key).Result()
			So(err, ShouldNotBeNil)
			// Then query it using the service
			_, err = GetDefaultLink().Query(ctx, "")
			So(err, ShouldNotBeNil)
		})
	})
}
