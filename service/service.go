package service

import (
	"sync/atomic"

	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xerror"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

const (
	SceneDefault = "default"
	ShintDefault = "default"
)

var (
	manager *Manager = &Manager{}
)

type Manager struct {
	links atomic.Pointer[map[string]*Link]
}

func Startup() error {
	manager.Reload()
	xconf.RegisterReload(manager.Reload)
	return nil
}

func (m *Manager) Reload() error {
	links := make(map[string]*Link)
	for k, v := range config.Get().Shint {
		link, err := NewLink(v)
		if err != nil {
			return err
		}
		links[k] = link
	}
	xlog.Debug("links", xlog.Any("links", links))
	m.links.Store(&links)
	return nil
}

func GetLink(scene string) (*Link, error) {
	if scene == "" {
		scene = SceneDefault
	}
	sc, ok := config.Get().Scene[scene]
	if !ok || sc.Disable {
		return nil, xerror.Errorf(
			ecode.BadRequest,
			xlog.String("cause", "scene not found"),
			xlog.String("scene", scene),
		)
	}
	key := sc.Shint
	if key == "" {
		key = ShintDefault
	}
	links := manager.links.Load()
	link, ok := (*links)[key]
	if !ok {
		return nil, xerror.Errorf(
			ecode.BadRequest,
			xlog.String("cause", "shint not found"),
			xlog.String("scene", scene),
		)
	}
	return link, nil
}

func GetDefaultLink() *Link {
	links := manager.links.Load()
	return (*links)[ShintDefault]
}
