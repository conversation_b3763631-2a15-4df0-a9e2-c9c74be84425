package service

import (
	"testing"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestManagerReload(t *testing.T) {
	Convey("test manager reload", t, func() {
		Convey("when reload with valid config", func() {
			manager := &Manager{}
			err := manager.Reload()
			So(err, ShouldBeNil)

			links := manager.links.Load()
			So(links, ShouldNotBeNil)
			So(*links, ShouldNotBeEmpty)

			defaultLink, ok := (*links)["default"]
			So(ok, ShouldBeTrue)
			So(defaultLink, ShouldNotBeNil)

			gameshareLink, ok := (*links)["gameshare"]
			So(ok, ShouldBeTrue)
			So(gameshareLink, ShouldNotBeNil)
		})

		Convey("when get link by scene", func() {
			defaultLink, err := GetLink("")
			So(err, ShouldBeNil)
			So(defaultLink, ShouldNotBeNil)

			defaultLink, err = GetLink("default")
			So(err, ShouldBeNil)
			So(defaultLink, ShouldNotBeNil)

			_, err = GetLink("nonexistent")
			So(err, ShouldNotBeNil)

			defaultLink = GetDefaultLink()
			So(defaultLink, ShouldNotBeNil)
		})

		Convey("when reload manager directly", func() {
			manager := &Manager{}
			err := manager.Reload()
			So(err, ShouldBeNil)

			links := manager.links.Load()
			So(links, ShouldNotBeNil)
			So(*links, ShouldNotContainKey, "")
		})
	})
}
