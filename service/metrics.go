package service

import "gitlab.papegames.com/fringe/sparrow/pkg/metric"

var (
	MetricsErrorOccurred = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "shorturl",
		Subsystem:   "",
		Name:        "error_occurred",
		Help:        "shorturl error occurred total",
		Labels:      []string{"kind", "reason"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsLinkCreate = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "shorturl",
		Subsystem:   "",
		Name:        "link_create_total",
		Help:        "shorturl link create total",
		Labels:      []string{"scene", "state"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsLinkQuery = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "shorturl",
		Subsystem:   "",
		Name:        "query_total",
		Help:        "shorturl query total",
		Labels:      []string{"kind", "state"},
		ConstLabels: metric.TargetLabels,
	})
)
