package service

import (
	"context"
	"errors"
	"net/url"

	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/shorturl/database"
	"gitlab.papegames.com/fringe/shorturl/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/shint"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/pattern/shint/provider"
	"gitlab.papegames.com/fringe/sparrow/pkg/xerror"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xmath/xrand"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring/radix"
)

const (
	Prefix = "link:"
)

var (
	charset      = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	shortCharset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	ranges       = [][]int{
		{6, 56800235584},
		{7, 3521614606208},
		{8, 218340105584896},
		{9, 13537086546263552},
		{10, 839299365868340224},
	}
)

type Link struct {
	// The idg of short link.
	idg *shint.Shint
	// The radix mapping of short link.
	rdx *radix.Radix
}

func NewLink(conf config.ShintConfig) (*Link, error) {
	cfg := shint.Config{
		Key:      conf.Key,
		KeyPath:  conf.KeyPath,
		Provider: "redis",
		Shard:    conf.Shard,
	}
	idg, err := cfg.Build()
	if err != nil {
		return nil, err
	}
	var charsetBytes []byte
	if conf.ShortCharset {
		charsetBytes = []byte(shortCharset)
	} else {
		charsetBytes = []byte(charset)
	}
	return &Link{
		idg: idg,
		rdx: radix.New(
			radix.Charset(
				charsetBytes,
			)),
	}, nil
}

func (l *Link) New(
	ctx context.Context,
	uri, scene string,
) (string, string, error) {
	if scene == "" {
		scene = SceneDefault
	}

	c := config.Get()
	cfg, ok := c.Scene[scene]
	if !ok || cfg.Disable {
		return "", "", xerror.Errorf(
			ecode.BadRequest,
			xlog.String("cause", "scene not found"),
			xlog.String("scene", scene),
		)
	}

	idStr, err := l.NewID(ctx)
	if err != nil {
		return "", "", xerror.Errorf(
			ecode.Internal,
			xlog.String("cause", "NewID with error"),
			xlog.String("scene", scene),
			xlog.Err(err),
		)
	}

	if cfg.Secret {
		idStr = idStr + ":" + string(xrand.Color1())
	}

	// 非独立的shint 才存储key到redis
	if cfg.Shint == "" {
		key := Prefix + idStr
		rdb := database.Get()
		err = rdb.SetNX(ctx, key, uri, cfg.TTL).Err()
		if err != nil {
			MetricsErrorOccurred.Inc("db", "redis_write_error")
			return "", "", xerror.Errorf(
				ecode.Internal,
				xlog.String("cause", "SETNX with error"),
				xlog.String("scene", scene),
				xlog.String("key", key),
				xlog.String("uri", uri),
				xlog.Err(err),
			)
		}
	}

	var path string
	if cfg.Host != "" {
		path, err = url.JoinPath(
			cfg.Host, idStr)
		if err != nil {
			return "", "", xerror.Errorf(
				ecode.Internal,
				xlog.String("cause", "url.JoinPath with error"),
				xlog.String("scene", scene),
				xlog.String("host", cfg.Host),
				xlog.Err(err),
			)
		}
	}
	return idStr, path, nil
}

func (l *Link) NewID(ctx context.Context) (string, error) {
	i, err := l.idg.Incr(ctx)
	if err != nil {
		MetricsErrorOccurred.Inc("db", "redis_write_error")
		return "", err
	}
	for _, r := range ranges {
		if i < r[1] {
			return l.rdx.Itoa(
				i, r[0]), nil
		}
	}
	return "", errors.New("link: out of range")
}

func (l *Link) Query(ctx context.Context, id string) (string, error) {
	key := Prefix + id
	rdb := database.Get()
	value, err := rdb.Get(ctx, key).Result()
	if err != nil {
		switch err {
		case xredis.Nil:
			return "", xerror.Errorf(
				shared.ErrCacheMiss,
				xlog.String("cause", "key not exists"),
				xlog.String("key", key),
				xlog.Err(err),
			)
		default:
			MetricsErrorOccurred.Inc("db", "redis_read_error")
			return "", xerror.Errorf(
				ecode.Internal,
				xlog.String("cause", "GET with error"),
				xlog.String("key", key),
				xlog.Err(err),
			)
		}
	}
	return value, nil
}
