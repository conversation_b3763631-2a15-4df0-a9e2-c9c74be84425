# Generated with protoc-gen-openapi
# https://gitlab.papegames.com/fringe/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ShorturlService API
    description: This API represents shorturl service.
    version: 0.0.1
servers:
    - url: https://shorturl.papegames.com
paths:
    /v1/shorturl/link:
        post:
            tags:
                - ShorturlService
            description: Creates a new link.
            operationId: Link
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/LinkRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/LinkResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/shorturl/query:
        post:
            tags:
                - ShorturlService
            description: Query a short link.
            operationId: Query
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/QueryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/StringValue'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/shorturl/redirect:
        get:
            tags:
                - ShorturlService
            description: Redirect a short link.
            operationId: Redirect
            parameters:
                - name: id
                  in: query
                  description: The redirect of the link.
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
components:
    schemas:
        LinkRequest:
            required:
                - redirect
            type: object
            properties:
                redirect:
                    maxLength: 4096
                    minLength: 1
                    type: string
                    description: The redirect of the link.
                scene:
                    type: string
                    description: The scene of the link.
            description: Request message for ShorturlService.Link.
        LinkResponse:
            type: object
            properties:
                id:
                    type: string
                    description: The id of the link.
                link:
                    type: string
                    description: The link url.
            description: Response message for ShorturlService.Link.
        QueryRequest:
            required:
                - id
            type: object
            properties:
                id:
                    maxLength: 32
                    minLength: 5
                    type: string
                    description: The redirect of the link.
            description: Request message for ShorturlService.Query.
        StringValue:
            type: object
            properties:
                value:
                    type: string
                    description: The string value.
            description: Wrapper message for `string`. The JSON representation for `StringValue` is JSON string.
        _failReturn:
            type: object
            properties:
                code:
                    example: 400
                    type: integer
                    format: int32
                info:
                    example: error
                    type: string
                request_id:
                    example: 16vHbfABAd
                    type: string
tags:
    - name: ShorturlService
