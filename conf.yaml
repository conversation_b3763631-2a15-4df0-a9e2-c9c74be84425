# service
host_http: ":9900"
host_grpc: ":9901"
register: false
scene:
  default:
    host: "https://t.cn"
    ttl: "5m"
  secret:
    host: "https://t.cn"
    ttl: "5m"
    secret: true   
  gameshare: 
    host: "https://t.cn"
    shint: gameshare
shint:
  default:
    shard: 128
    key: "link:shint"
    keypath: sparrow.database.redis
  gameshare:
    shard: 128
    key: "link:gameshare:shint"
    keypath: sparrow.database.redis
    short_charset: true
sparrow:
  log:
    file: "./logs/shorturl"
    level: "debug"
    encoding: "console"
    rotate: "daily"
    buffer: 4096

  govern:
    enable: true
    host: "internal:9902"

  database:
    redis:
      addr: "127.0.0.1:16381"