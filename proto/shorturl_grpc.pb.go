// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: proto/shorturl.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ShorturlService_Link_FullMethodName     = "/papegames.sparrow.shorturl.ShorturlService/Link"
	ShorturlService_Query_FullMethodName    = "/papegames.sparrow.shorturl.ShorturlService/Query"
	ShorturlService_Redirect_FullMethodName = "/papegames.sparrow.shorturl.ShorturlService/Redirect"
)

// ShorturlServiceClient is the client API for ShorturlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// This API represents shorturl service.
type ShorturlServiceClient interface {
	// Creates a new link.
	Link(ctx context.Context, in *LinkRequest, opts ...grpc.CallOption) (*LinkResponse, error)
	// Query a short link.
	Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	// Redirect a short link.
	Redirect(ctx context.Context, in *RedirectRequest, opts ...grpc.CallOption) (*RedirectResponse, error)
}

type shorturlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewShorturlServiceClient(cc grpc.ClientConnInterface) ShorturlServiceClient {
	return &shorturlServiceClient{cc}
}

func (c *shorturlServiceClient) Link(ctx context.Context, in *LinkRequest, opts ...grpc.CallOption) (*LinkResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LinkResponse)
	err := c.cc.Invoke(ctx, ShorturlService_Link_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shorturlServiceClient) Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, ShorturlService_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shorturlServiceClient) Redirect(ctx context.Context, in *RedirectRequest, opts ...grpc.CallOption) (*RedirectResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RedirectResponse)
	err := c.cc.Invoke(ctx, ShorturlService_Redirect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShorturlServiceServer is the server API for ShorturlService service.
// All implementations must embed UnimplementedShorturlServiceServer
// for forward compatibility.
//
// This API represents shorturl service.
type ShorturlServiceServer interface {
	// Creates a new link.
	Link(context.Context, *LinkRequest) (*LinkResponse, error)
	// Query a short link.
	Query(context.Context, *QueryRequest) (*wrapperspb.StringValue, error)
	// Redirect a short link.
	Redirect(context.Context, *RedirectRequest) (*RedirectResponse, error)
	mustEmbedUnimplementedShorturlServiceServer()
}

// UnimplementedShorturlServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShorturlServiceServer struct{}

func (UnimplementedShorturlServiceServer) Link(context.Context, *LinkRequest) (*LinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Link not implemented")
}
func (UnimplementedShorturlServiceServer) Query(context.Context, *QueryRequest) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (UnimplementedShorturlServiceServer) Redirect(context.Context, *RedirectRequest) (*RedirectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Redirect not implemented")
}
func (UnimplementedShorturlServiceServer) mustEmbedUnimplementedShorturlServiceServer() {}
func (UnimplementedShorturlServiceServer) testEmbeddedByValue()                         {}

// UnsafeShorturlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShorturlServiceServer will
// result in compilation errors.
type UnsafeShorturlServiceServer interface {
	mustEmbedUnimplementedShorturlServiceServer()
}

func RegisterShorturlServiceServer(s grpc.ServiceRegistrar, srv ShorturlServiceServer) {
	// If the following call pancis, it indicates UnimplementedShorturlServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ShorturlService_ServiceDesc, srv)
}

func _ShorturlService_Link_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShorturlServiceServer).Link(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShorturlService_Link_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShorturlServiceServer).Link(ctx, req.(*LinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShorturlService_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShorturlServiceServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShorturlService_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShorturlServiceServer).Query(ctx, req.(*QueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShorturlService_Redirect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedirectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShorturlServiceServer).Redirect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShorturlService_Redirect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShorturlServiceServer).Redirect(ctx, req.(*RedirectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShorturlService_ServiceDesc is the grpc.ServiceDesc for ShorturlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShorturlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.shorturl.ShorturlService",
	HandlerType: (*ShorturlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Link",
			Handler:    _ShorturlService_Link_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _ShorturlService_Query_Handler,
		},
		{
			MethodName: "Redirect",
			Handler:    _ShorturlService_Redirect_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/shorturl.proto",
}
