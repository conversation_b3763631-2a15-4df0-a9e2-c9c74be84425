// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/shorturl.proto

package proto

func (x *LinkRequest) Validate() error {
	if len(x.GetRedirect()) == 0 {
		return LinkRequestValidationError{
			field:   "Redirect",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetRedirect()) < 1 {
		return LinkRequestValidationError{
			field:   "Redirect",
			reason:  "min_length",
			message: "value must be at least 1 bytes",
		}
	}
	if len(x.GetRedirect()) > 4096 {
		return LinkRequestValidationError{
			field:   "Redirect",
			reason:  "max_length",
			message: "value length must be at most 4096 bytes",
		}
	}
	return nil
}

func (x *LinkResponse) Validate() error {
	return nil
}

func (x *QueryRequest) Validate() error {
	if len(x.GetId()) == 0 {
		return QueryRequestValidationError{
			field:   "Id",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetId()) < 5 {
		return QueryRequestValidationError{
			field:   "Id",
			reason:  "min_length",
			message: "value must be at least 5 bytes",
		}
	}
	if len(x.GetId()) > 32 {
		return QueryRequestValidationError{
			field:   "Id",
			reason:  "max_length",
			message: "value length must be at most 32 bytes",
		}
	}
	return nil
}

func (x *RedirectRequest) Validate() error {
	if len(x.GetId()) == 0 {
		return RedirectRequestValidationError{
			field:   "Id",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetId()) < 5 {
		return RedirectRequestValidationError{
			field:   "Id",
			reason:  "min_length",
			message: "value must be at least 5 bytes",
		}
	}
	if len(x.GetId()) > 32 {
		return RedirectRequestValidationError{
			field:   "Id",
			reason:  "max_length",
			message: "value length must be at most 32 bytes",
		}
	}
	return nil
}

func (x *RedirectResponse) Validate() error {
	return nil
}

type LinkRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e LinkRequestValidationError) Field() string { return e.field }

func (e LinkRequestValidationError) Reason() string { return e.reason }

func (e LinkRequestValidationError) Message() string { return e.message }

func (e LinkRequestValidationError) Cause() error { return e.cause }

func (e LinkRequestValidationError) ErrorName() string { return "LinkRequestValidationError" }

func (e LinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid LinkRequest." + e.field + ": " + e.message + cause
}

type LinkResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e LinkResponseValidationError) Field() string { return e.field }

func (e LinkResponseValidationError) Reason() string { return e.reason }

func (e LinkResponseValidationError) Message() string { return e.message }

func (e LinkResponseValidationError) Cause() error { return e.cause }

func (e LinkResponseValidationError) ErrorName() string { return "LinkResponseValidationError" }

func (e LinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid LinkResponse." + e.field + ": " + e.message + cause
}

type QueryRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QueryRequestValidationError) Field() string { return e.field }

func (e QueryRequestValidationError) Reason() string { return e.reason }

func (e QueryRequestValidationError) Message() string { return e.message }

func (e QueryRequestValidationError) Cause() error { return e.cause }

func (e QueryRequestValidationError) ErrorName() string { return "QueryRequestValidationError" }

func (e QueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QueryRequest." + e.field + ": " + e.message + cause
}

type RedirectRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RedirectRequestValidationError) Field() string { return e.field }

func (e RedirectRequestValidationError) Reason() string { return e.reason }

func (e RedirectRequestValidationError) Message() string { return e.message }

func (e RedirectRequestValidationError) Cause() error { return e.cause }

func (e RedirectRequestValidationError) ErrorName() string { return "RedirectRequestValidationError" }

func (e RedirectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RedirectRequest." + e.field + ": " + e.message + cause
}

type RedirectResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RedirectResponseValidationError) Field() string { return e.field }

func (e RedirectResponseValidationError) Reason() string { return e.reason }

func (e RedirectResponseValidationError) Message() string { return e.message }

func (e RedirectResponseValidationError) Cause() error { return e.cause }

func (e RedirectResponseValidationError) ErrorName() string { return "RedirectResponseValidationError" }

func (e RedirectResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RedirectResponse." + e.field + ": " + e.message + cause
}
