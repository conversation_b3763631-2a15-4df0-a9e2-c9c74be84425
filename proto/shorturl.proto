syntax = "proto3";

package papegames.sparrow.shorturl;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/wrappers.proto";
import "openapiv3/annotations.proto";

option go_package = "gitlab.papegames.com/fringe/shorturl/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "ShorturlProto";
option java_package = "com.papegames.sparrow.shorturl";

// This API represents shorturl service.
service ShorturlService {
  option (google.api.default_host) = "shorturl.papegames.com";

  // Creates a new link.
  rpc Link(LinkRequest) returns (LinkResponse) {
    option (google.api.http) = {
      post: "/v1/shorturl/link",
      body: "*",
    };
    option (google.api.method_signature) = "form-data";
  }

  // Query a short link.
  rpc Query(QueryRequest) returns (google.protobuf.StringValue) {
    option (google.api.http) = {
      post: "/v1/shorturl/query",
      body: "*",
    };
    option (google.api.method_signature) = "form-data";
  }

  // Redirect a short link.
  rpc Redirect(RedirectRequest) returns (RedirectResponse) {
    option (google.api.http) = {
      get: "/v1/shorturl/redirect"
    };
    option (google.api.method_signature) = "redirect";
  }
}

// Request message for ShorturlService.Link.
message LinkRequest {
  // The redirect of the link.
  string redirect = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 1,
      max_length: 4096,
    }
  ];
  // The scene of the link.
  string scene = 2;
}

// Response message for ShorturlService.Link.
message LinkResponse {
  // The id of the link.
  string id = 1;
  // The link url.
  string link = 2;
}

// Request message for ShorturlService.Query.
message QueryRequest {
  // The redirect of the link.
  string id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 5,
      max_length: 32,
    }
  ];
}

// Request message for ShorturlService.Redirect.
message RedirectRequest {
  // The redirect of the link.
  string id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 5,
      max_length: 32,
    }
  ];
}

// Response message for ShorturlService.Redirect.
message RedirectResponse {}
