// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.5.0
// protoc          v4.25.1
// source: shorturl
package proto

import (
	context "context"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	hooks "gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func RegisterShorturlServiceGinServer(s *xgin.Server, srv ShorturlServiceServer) {
	eng := s.GetGinEngine()
	eng.Use(hooks.GetHandlerFunc()...)
	xgin.RegisterHandler(eng,
		"ANY", "/v1/shorturl/link",
		_ShorturlServiceGin_Link_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/shorturl/link", "Link")
	xgin.RegisterHandler(eng,
		"ANY", "/v1/shorturl/query",
		_ShorturlServiceGin_Query_Handler(srv, s.HTTPInterceptor()),
		"ANY /v1/shorturl/query", "Query")
	xgin.RegisterHandler(eng,
		"GET", "/v1/shorturl/redirect",
		_ShorturlServiceGin_Redirect_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/shorturl/redirect", "Redirect")
}

func _ShorturlServiceGin_Link_Handler(srv ShorturlServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/shorturl/link",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Link(ctx, in.(*LinkRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(LinkRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Link(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _ShorturlServiceGin_Query_Handler(srv ShorturlServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "ANY /v1/shorturl/query",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Query(ctx, in.(*QueryRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(QueryRequest)
		if err := xgin.DecodeValues(in, c, true); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Query(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _ShorturlServiceGin_Redirect_Handler(srv ShorturlServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/shorturl/redirect",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Redirect(ctx, in.(*RedirectRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(RedirectRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Redirect(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}
