// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: proto/shorturl.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for ShorturlService.Link.
type LinkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The redirect of the link.
	Redirect string `protobuf:"bytes,1,opt,name=redirect,proto3" json:"redirect,omitempty"`
	// The scene of the link.
	Scene         string `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkRequest) Reset() {
	*x = LinkRequest{}
	mi := &file_proto_shorturl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkRequest) ProtoMessage() {}

func (x *LinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shorturl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkRequest.ProtoReflect.Descriptor instead.
func (*LinkRequest) Descriptor() ([]byte, []int) {
	return file_proto_shorturl_proto_rawDescGZIP(), []int{0}
}

func (x *LinkRequest) GetRedirect() string {
	if x != nil {
		return x.Redirect
	}
	return ""
}

func (x *LinkRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

// Response message for ShorturlService.Link.
type LinkResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The id of the link.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The link url.
	Link          string `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkResponse) Reset() {
	*x = LinkResponse{}
	mi := &file_proto_shorturl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkResponse) ProtoMessage() {}

func (x *LinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shorturl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkResponse.ProtoReflect.Descriptor instead.
func (*LinkResponse) Descriptor() ([]byte, []int) {
	return file_proto_shorturl_proto_rawDescGZIP(), []int{1}
}

func (x *LinkResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LinkResponse) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

// Request message for ShorturlService.Query.
type QueryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The redirect of the link.
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryRequest) Reset() {
	*x = QueryRequest{}
	mi := &file_proto_shorturl_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRequest) ProtoMessage() {}

func (x *QueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shorturl_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRequest.ProtoReflect.Descriptor instead.
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return file_proto_shorturl_proto_rawDescGZIP(), []int{2}
}

func (x *QueryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Request message for ShorturlService.Redirect.
type RedirectRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The redirect of the link.
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedirectRequest) Reset() {
	*x = RedirectRequest{}
	mi := &file_proto_shorturl_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedirectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedirectRequest) ProtoMessage() {}

func (x *RedirectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shorturl_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedirectRequest.ProtoReflect.Descriptor instead.
func (*RedirectRequest) Descriptor() ([]byte, []int) {
	return file_proto_shorturl_proto_rawDescGZIP(), []int{3}
}

func (x *RedirectRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Response message for ShorturlService.Redirect.
type RedirectResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedirectResponse) Reset() {
	*x = RedirectResponse{}
	mi := &file_proto_shorturl_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedirectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedirectResponse) ProtoMessage() {}

func (x *RedirectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shorturl_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedirectResponse.ProtoReflect.Descriptor instead.
func (*RedirectResponse) Descriptor() ([]byte, []int) {
	return file_proto_shorturl_proto_rawDescGZIP(), []int{4}
}

var File_proto_shorturl_proto protoreflect.FileDescriptor

const file_proto_shorturl_proto_rawDesc = "" +
	"\n" +
	"\x14proto/shorturl.proto\x12\x1apapegames.sparrow.shorturl\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1bopenapiv3/annotations.proto\"N\n" +
	"\vLinkRequest\x12)\n" +
	"\bredirect\x18\x01 \x01(\tB\r\xe2A\x01\x02\xbaG\x06x\x80 \x80\x01\x01R\bredirect\x12\x14\n" +
	"\x05scene\x18\x02 \x01(\tR\x05scene\"2\n" +
	"\fLinkResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04link\x18\x02 \x01(\tR\x04link\",\n" +
	"\fQueryRequest\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\tB\f\xe2A\x01\x02\xbaG\x05x \x80\x01\x05R\x02id\"/\n" +
	"\x0fRedirectRequest\x12\x1c\n" +
	"\x02id\x18\x01 \x01(\tB\f\xe2A\x01\x02\xbaG\x05x \x80\x01\x05R\x02id\"\x12\n" +
	"\x10RedirectResponse2\xc0\x03\n" +
	"\x0fShorturlService\x12\x83\x01\n" +
	"\x04Link\x12'.papegames.sparrow.shorturl.LinkRequest\x1a(.papegames.sparrow.shorturl.LinkResponse\"(\xdaA\tform-data\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/shorturl/link\x12z\n" +
	"\x05Query\x12(.papegames.sparrow.shorturl.QueryRequest\x1a\x1c.google.protobuf.StringValue\")\xdaA\tform-data\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/shorturl/query\x12\x8f\x01\n" +
	"\bRedirect\x12+.papegames.sparrow.shorturl.RedirectRequest\x1a,.papegames.sparrow.shorturl.RedirectResponse\"(\xdaA\bredirect\x82\xd3\xe4\x93\x02\x17\x12\x15/v1/shorturl/redirect\x1a\x19\xcaA\x16shorturl.papegames.comBc\n" +
	"\x1ecom.papegames.sparrow.shorturlB\rShorturlProtoP\x01Z0gitlab.papegames.com/fringe/shorturl/proto;protob\x06proto3"

var (
	file_proto_shorturl_proto_rawDescOnce sync.Once
	file_proto_shorturl_proto_rawDescData []byte
)

func file_proto_shorturl_proto_rawDescGZIP() []byte {
	file_proto_shorturl_proto_rawDescOnce.Do(func() {
		file_proto_shorturl_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_shorturl_proto_rawDesc), len(file_proto_shorturl_proto_rawDesc)))
	})
	return file_proto_shorturl_proto_rawDescData
}

var file_proto_shorturl_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_shorturl_proto_goTypes = []any{
	(*LinkRequest)(nil),            // 0: papegames.sparrow.shorturl.LinkRequest
	(*LinkResponse)(nil),           // 1: papegames.sparrow.shorturl.LinkResponse
	(*QueryRequest)(nil),           // 2: papegames.sparrow.shorturl.QueryRequest
	(*RedirectRequest)(nil),        // 3: papegames.sparrow.shorturl.RedirectRequest
	(*RedirectResponse)(nil),       // 4: papegames.sparrow.shorturl.RedirectResponse
	(*wrapperspb.StringValue)(nil), // 5: google.protobuf.StringValue
}
var file_proto_shorturl_proto_depIdxs = []int32{
	0, // 0: papegames.sparrow.shorturl.ShorturlService.Link:input_type -> papegames.sparrow.shorturl.LinkRequest
	2, // 1: papegames.sparrow.shorturl.ShorturlService.Query:input_type -> papegames.sparrow.shorturl.QueryRequest
	3, // 2: papegames.sparrow.shorturl.ShorturlService.Redirect:input_type -> papegames.sparrow.shorturl.RedirectRequest
	1, // 3: papegames.sparrow.shorturl.ShorturlService.Link:output_type -> papegames.sparrow.shorturl.LinkResponse
	5, // 4: papegames.sparrow.shorturl.ShorturlService.Query:output_type -> google.protobuf.StringValue
	4, // 5: papegames.sparrow.shorturl.ShorturlService.Redirect:output_type -> papegames.sparrow.shorturl.RedirectResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_shorturl_proto_init() }
func file_proto_shorturl_proto_init() {
	if File_proto_shorturl_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_shorturl_proto_rawDesc), len(file_proto_shorturl_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_shorturl_proto_goTypes,
		DependencyIndexes: file_proto_shorturl_proto_depIdxs,
		MessageInfos:      file_proto_shorturl_proto_msgTypes,
	}.Build()
	File_proto_shorturl_proto = out.File
	file_proto_shorturl_proto_goTypes = nil
	file_proto_shorturl_proto_depIdxs = nil
}
