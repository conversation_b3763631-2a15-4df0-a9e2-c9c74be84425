package server

import (
	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/shorturl/control"
	"gitlab.papegames.com/fringe/shorturl/proto"

	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
)

var ginServer = xgin.NewServer()

func GetHTTP() *xgin.Server { return ginServer }

func StartupHTTP() error {
	ginServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().HostHTTP),
		server.ServiceRegistrar(config.Get().Register),
		server.HTTPServerControl(control.Get()),
		server.HTTPServerRegister(proto.RegisterShorturlServiceGinServer),
	)
	return nil
}
