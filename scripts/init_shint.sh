#!/bin/bash

# 初始化shint key的脚本
# 使用4位随机数字 * (shard_index+1) * 10000 作为初始值

# 默认配置
REDIS_HOST="127.0.0.1"
REDIS_PORT="6379"
REDIS_AUTH=""
SHARD_COUNT=128
SHINT_KEY="link:gameshare:shint"

# 解析命令行参数
while [ $# -gt 0 ]; do
    case $1 in
        -h|--host)
            REDIS_HOST="$2"
            shift 2
            ;;
        -p|--port)
            REDIS_PORT="$2"
            shift 2
            ;;
        -a|--auth)
            REDIS_AUTH="$2"
            shift 2
            ;;
        -k|--key)
            SHINT_KEY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -h, --host     Redis host (default: 127.0.0.1)"
            echo "  -p, --port     Redis port (default: 6379)"
            echo "  -a, --auth     Redis password (optional)"
            echo "  -k, --key      Shint key prefix (default: link:gameshare:shint)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# 检查redis-cli是否存在
if ! command -v redis-cli &> /dev/null; then
    echo "Error: redis-cli not found"
    exit 1
fi

# 构建Redis CLI命令
REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
if [ -n "$REDIS_AUTH" ]; then
    REDIS_CMD="$REDIS_CMD -a $REDIS_AUTH"
fi

echo "Initializing shint key: $SHINT_KEY"
echo "Shard count: $SHARD_COUNT"
echo "Redis: $REDIS_HOST:$REDIS_PORT"
if [ -n "$REDIS_AUTH" ]; then
    echo "Auth: enabled"
else
    echo "Auth: disabled"
fi
echo ""

# 初始化每个shard
for ((i=0; i<SHARD_COUNT; i++)); do
    # 生成4位随机数字
    random_num=$(shuf -i 1000-9999 -n 1)
    # 计算初始值: 4位随机数 * (shard_index + 1)
    init_value=$((random_num *10000 * (i+1)))
    
    # Redis key格式: link:gameshare:shint:shard_index
    redis_key="${SHINT_KEY}:${i}"
    
    echo "Setting $redis_key = $init_value (random: $random_num)"
    
    # 设置Redis key
    $REDIS_CMD SET "$redis_key" "$init_value" > /dev/null
    
    if [ $? -eq 0 ]; then
        echo "✓ Successfully set $redis_key"
    else
        echo "✗ Failed to set $redis_key"
        exit 1
    fi
done

echo ""
echo "Shint initialization completed!"
echo "Initialized $SHARD_COUNT shards for key: $SHINT_KEY"
