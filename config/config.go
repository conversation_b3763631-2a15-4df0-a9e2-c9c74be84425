package config

import (
	"sync/atomic"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type ShintConfig struct {
	ShortCharset bool `xconf:"short_charset"`
	Shard        int
	Provider     string
	KeyPath      string
	Key          string
}

type Config struct {
	HostHTTP string `xconf:"host_http"`
	HostGRPC string `xconf:"host_grpc"`
	Register bool   `xconf:"register"`
	// The scene config for short url.
	Scene map[string]struct {
		Disable bool          `xconf:"disable"`
		Host    string        `xconf:"host"`
		Default string        `xconf:"default"`
		TTL     time.Duration `xconf:"ttl"`
		Secret  bool          `xconf:"secret"` // use secret mode
		Shint   string        `xconf:"shint"`
	} `xconf:"scene"`
	Shint map[string]ShintConfig `xconf:"shint"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		HostHTTP: "",
		HostGRPC: "",
		Register: false,
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(c)
	return nil
}
