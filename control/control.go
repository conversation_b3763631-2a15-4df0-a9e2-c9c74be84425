package control

import (
	"context"
	"errors"
	"net/http"

	"gitlab.papegames.com/fringe/shorturl/proto"
	"gitlab.papegames.com/fringe/shorturl/service"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"

	pb "google.golang.org/protobuf/types/known/wrapperspb"
)

var ins = new(Control)

type Control struct {
	proto.UnimplementedShorturlServiceServer
}

func Get() *Control {
	return ins
}

func (Control) Link(ctx context.Context, request *proto.LinkRequest) (*proto.LinkResponse, error) {
	shint, err := service.GetLink(request.Scene)
	if err != nil {
		xlog.FromContext(ctx).Error("Link with error",
			xlog.String("redirect", request.Redirect),
			xlog.String("scene", request.Scene),
			xlog.Err(err))
		service.MetricsLinkCreate.Inc(request.Scene, "err")
		return nil, err
	}

	idStr, link, err := shint.New(ctx,
		request.Redirect,
		request.Scene,
	)

	if err != nil {
		xlog.FromContext(ctx).Error("Link with error",
			xlog.String("redirect", request.Redirect),
			xlog.String("scene", request.Scene),
			xlog.Err(err))
		service.MetricsLinkCreate.Inc(request.Scene, "err")
		return nil, err
	}
	service.MetricsLinkCreate.Inc(request.Scene, "ok")
	return &proto.LinkResponse{
		Id:   idStr,
		Link: link,
	}, nil
}

func (Control) Query(ctx context.Context, request *proto.QueryRequest) (*pb.StringValue, error) {
	value, err := service.GetDefaultLink().Query(ctx, request.Id)
	if err != nil {
		xlog.FromContext(ctx).Error("Query with error",
			xlog.String("id", request.Id),
			xlog.Err(err))
		service.MetricsLinkQuery.Inc("query", "err")
		return nil, err
	}
	service.MetricsLinkQuery.Inc("query", "ok")
	return pb.String(value), nil
}

func (Control) Redirect(ctx context.Context, request *proto.RedirectRequest) (*proto.RedirectResponse, error) {
	c := xgin.FromContext(ctx)
	if c == nil {
		xlog.FromContext(ctx).Error("Redirect with error",
			xlog.Err(errors.New("support HTTP request only")))
		return nil, ecode.BadRequest
	}

	value, err := service.GetDefaultLink().Query(ctx, request.Id)
	if err != nil {
		xlog.FromContext(ctx).Error("Redirect with error",
			xlog.String("id", request.Id),
			xlog.Err(err))
		service.MetricsLinkQuery.Inc("redirect", "err")
		return nil, err
	}
	service.MetricsLinkQuery.Inc("redirect", "ok")
	// redirect to original link.
	c.Redirect(http.StatusTemporaryRedirect, value)
	return nil, ecode.ResetContent
}
