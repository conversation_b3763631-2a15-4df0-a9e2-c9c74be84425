package integration

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path"
	"runtime"
	"sync"
	"testing"
	"time"

	"gitlab.papegames.com/fringe/shorturl/config"
	"gitlab.papegames.com/fringe/shorturl/database"
	"gitlab.papegames.com/fringe/shorturl/server"
	"gitlab.papegames.com/fringe/shorturl/service"

	"gitlab.papegames.com/fringe/sparrow"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/compose"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpexpect"
)

var expect = xconvey.NewExpector(server.GetHTTP().GetGinEngine())

func TestMain(m *testing.M) {
	_, filename, _, _ := runtime.Caller(0)
	c := path.Join(path.Dir(filename), "docker-compose.yaml")
	compose.Setup(c)
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		server.Startup,
		service.Startup,
	).Server(server.Get()...)

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()

	waitReady(app)
	fmt.Println("TestMain start")
	exitCode := m.Run()
	app.Stop()
	wg.Wait()
	fmt.Println("TestMain done")
	compose.Down()
	os.Exit(exitCode)
}

func waitReady(app *sparrow.Application) {
	app.WaitReady(context.Background())
	for i := 0; i < 30; i++ {
		resp, err := http.Get("http://" + config.Get().HostHTTP + "/-/health")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			return
		}
		time.Sleep(time.Millisecond * 100)
	}
	panic("waiting too long")
}

func requestObjectExpect(obj *xhttpexpect.Object, withoutData ...bool) {
	var keys []interface{}
	if len(withoutData) != 0 && withoutData[0] {
		keys = []interface{}{"code", "info", "request_id"}
	} else {
		keys = []interface{}{"code", "info", "request_id", "data"}
	}
	obj.Keys().ContainsOnly(keys...)
	obj.Value("code").IsEqual(0)
	obj.Value("info").IsEqual("OK")
	obj.Value("request_id").NotNull()
}
