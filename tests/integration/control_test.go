package integration

import (
	"net/http"
	"testing"

	"github.com/gavv/httpexpect/v2"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestQuery(t *testing.T) {
	Convey("test query", t, func(c C) {
		Convey("test default scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.baidu.com").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			r = expect.Build(c).
				POST("/v1/shorturl/query").
				WithQueryObject(map[string]string{
					"id": id,
				}).
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)

			r = expect.Build(c).
				POST("/v1/shorturl/query").
				WithQueryObject(map[string]string{
					"id": "77777777",
				}).
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().IsEqual(10001) //cache miss
		})
		Convey("test secret scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.baidu.com").
				WithFormField("scene", "secret").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			r = expect.Build(c).
				POST("/v1/shorturl/query").
				WithQueryObject(map[string]string{
					"id": id,
				}).
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
		})

		// 测试只用来做自增的场景测试
		Convey("test gameshare  scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.baidu.com").
				WithFormField("scene", "gameshare").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			r = expect.Build(c).
				POST("/v1/shorturl/query").
				WithQueryObject(map[string]string{
					"id": id,
				}).
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().NotEqual(0)

		})

	})
}

func TestLink(t *testing.T) {
	Convey("test link", t, func(c C) {
		Convey("test default scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.baidu.com").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
		})

		Convey("test secret scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.google.com").
				WithFormField("scene", "secret").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			So(len(id), ShouldEqual, 17) // secret模式ID长度为17
			So(id[6], ShouldEqual, ':')  // 第7位应该是分隔符
		})

		Convey("test with empty redirect", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().NotEqual(0)
		})

		Convey("test with invalid scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.example.com").
				WithFormField("scene", "invalid_scene").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().NotEqual(0) // invalid argument
		})
	})
}

func TestRedirect(t *testing.T) {
	Convey("test redirect", t, func(c C) {
		Convey("test normal redirect", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.baidu.com").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			_ = expect.Build(c).
				GET("/v1/shorturl/redirect").
				WithQuery("id", id).
				WithRedirectPolicy(httpexpect.DontFollowRedirects).
				Expect().
				Status(http.StatusTemporaryRedirect)
		})

		Convey("test redirect with secret scene", func(c C) {
			r := expect.Build(c).
				POST("/v1/shorturl/link").
				WithFormField("redirect", "http://www.google.com").
				WithFormField("scene", "secret").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			requestObjectExpect(r)
			r.Value("code").Number().IsEqual(0)
			id := r.Value("data").Object().Value("id").String().Raw()
			_ = expect.Build(c).
				GET("/v1/shorturl/redirect").
				WithQuery("id", id).
				WithRedirectPolicy(httpexpect.DontFollowRedirects).
				Expect().
				Status(http.StatusTemporaryRedirect)
		})

		Convey("test redirect with non-existent id", func(c C) {
			r := expect.Build(c).
				GET("/v1/shorturl/redirect").
				WithQuery("id", "nonexistent").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().IsEqual(10001) // cache miss
		})

		Convey("test redirect with empty id", func(c C) {
			r := expect.Build(c).
				GET("/v1/shorturl/redirect").
				WithQuery("id", "").
				Expect().
				Status(http.StatusOK).
				JSON().Object()
			r.Value("code").Number().IsEqual(400) // bad request
		})
	})
}
